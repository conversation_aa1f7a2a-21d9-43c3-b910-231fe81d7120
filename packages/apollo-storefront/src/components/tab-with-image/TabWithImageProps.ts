import { Tabs } from "@apollo/ui"
import { ComponentProps, HTMLAttributes, PropsWithChildren } from "react"
import type { CategoryMenuItemProps } from "../category-menu-item/CategoryMenuItemProps"


export type TabWithImageRootProps = ComponentProps<typeof Tabs.Root>
export type TabWithImageListProps = ComponentProps<typeof Tabs.List>
export type TabProps = ComponentProps<typeof Tabs.Tab> & {
  imageSrc?: string
  imageAlt?: string
  imageSrcSet?: string
  icon?: React.ReactNode
  label?: string
  renderCategoryMenuItem?: (props: CategoryMenuItemProps) => React.ReactNode
}

export type TabWithImageIndicatorProps = ComponentProps<typeof Tabs.Indicator>
export type TabWithImagePanelProps = ComponentProps<typeof Tabs.Panel>