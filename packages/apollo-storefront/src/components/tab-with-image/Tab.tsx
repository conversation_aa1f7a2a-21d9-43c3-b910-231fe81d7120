import { Tabs } from "@apollo/ui"
import classNames from "classnames"

import { CategoryMenuItem } from "../category-menu-item"
import styles from "./tab-with-image.module.css"
import type { TabProps } from "./TabWithImageProps"

export function Tab({
  className,
  children,
  imageSrc,
  imageAlt,
  imageSrcSet,
  icon,
  label,
  renderCategoryMenuItem,
  ...props
}: TabProps) {

  return (
    <Tabs.Tab
      className={classNames(
        "ApolloTabWithImage-tab",
        styles.tabWithImageItem,
        className
      )}
      {...props}
    >
      {renderCategoryMenuItem ? (
        renderCategoryMenuItem({
          imageSrc,
          imageAlt,
          imageSrcSet,
          icon,
          label,
          maxLines: 2,
          className: styles.categoryMenuItemOverride,
          disabled: props.disabled,
        })
      ) : (
        <CategoryMenuItem
          imageSrc={imageSrc}
          imageAlt={imageAlt}
          imageSrcSet={imageSrcSet}
          icon={icon}
          label={label}
          maxLines={2}
          className={styles.categoryMenuItemOverride}
          disabled={props.disabled}
        />
      )}
    </Tabs.Tab>
  )
}
