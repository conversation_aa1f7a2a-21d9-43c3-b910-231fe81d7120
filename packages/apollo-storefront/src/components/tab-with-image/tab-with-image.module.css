/* Tab Root Styles */
.tabWithImageRoot {
    --apl-tabs-item-border-bottom-color: transparent;
}

/* Tab List Styles - Scrollable */
.tabWithImageList {
    display: flex;
    align-items: stretch;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    position: relative;
    flex-wrap: nowrap;
    border-bottom: none !important;

    &::-webkit-scrollbar {
      display: none;
    }
}


/* Tab Item Styles */
.tabWithImageItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start !important;
    gap: var(--apl-alias-spacing-gap-gap3, 4px);
    padding: var(--apl-alias-spacing-padding-padding5, 8px) var(--apl-alias-spacing-padding-padding3, 4px);
    flex-shrink: 0;
    min-width: 74px;
    max-width: 440px;
    min-height: 42px;
    border-bottom: none;
}

/* Text content with line-clamp */
.tabWithImageText {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    font-size: var(--apl-alias-typography-body-small-font-size);
    line-height: var(--apl-alias-typography-body-small-line-height);
    color: inherit;
    min-width: 0;
    max-width: 66px;
    word-wrap: break-word;
    hyphens: auto;
}

/* Tab Indicator Styles */
.tabWithImageIndicator {
    height: 3px;
    width: var(--active-tab-width) !important;
}

/* Image Container */
.tabImageRoot {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 32px;
    height: 32px;
}

/* Image Styles */
.tabImage {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: var(--apl-alias-radius-radius2, 4px);
    display: block;
}

/* CategoryMenuItem override styles to match Tab appearance */
.categoryMenuItemOverride {
    /* Reset CategoryMenuItem styles to match original Tab layout */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start !important;
    gap: var(--apl-alias-spacing-gap-gap3, 4px);
    padding: 0; /* Remove CategoryMenuItem padding, let Tab handle it */
    flex-shrink: 0;
    min-width: auto; /* Let Tab control width */
    min-height: auto; /* Let Tab control height */
    border-bottom: none;
    background: transparent !important; /* Remove CategoryMenuItem background */
    border-radius: 0 !important; /* Remove CategoryMenuItem border radius */
}

.categoryMenuItemOverride:hover {
    background: transparent !important; /* Remove CategoryMenuItem hover background */
}

/* Override CategoryMenuItem image root to match TabImage styles */
.categoryMenuItemOverride .ApolloCategoryMenuItem-imageRoot {
    width: 32px;
    height: 32px;
}

/* Override CategoryMenuItem image to match TabImage styles */
.categoryMenuItemOverride .ApolloCategoryMenuItem-img {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: var(--apl-alias-radius-radius2, 4px);
    display: block;
}

/* Override CategoryMenuItem label to match TabText styles */
.categoryMenuItemOverride .ApolloCategoryMenuItem-label {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    font-size: var(--apl-alias-typography-body-small-font-size);
    line-height: var(--apl-alias-typography-body-small-line-height);
    color: inherit;
    min-width: 0;
    max-width: 66px;
    word-wrap: break-word;
    hyphens: auto;
}


